# Use variables to customize the deployment

variable "root_id" {
  type        = string
  description = "Sets the value used for generating unique resource naming within the module."
  default     = "ewh"
}

variable "root_name" {
  type        = string
  description = "Sets the value used for the \"intermediate root\" management group display name."
  default     = "EWH"
}

variable "primary_location" {
  type        = string
  description = "Sets the location for \"primary\" resources to be created in."
  default     = "southeastasia"
}

variable "secondary_location" {
  type        = string
  description = "Sets the location for \"secondary\" resources to be created in."
  default     = "eastasia"
}

variable "email_security_contact" {
  type        = string
  description = "Set a custom value for the security contact email address."
  default     = "<email_security_contact>"
}

variable "log_retention_in_days" {
  type        = number
  description = "Set a custom value for how many days to store logs in the Log Analytics workspace."
  default     = 60
}

variable "enable_ddos_protection" {
  type        = bool
  description = "Controls whether to create a DDoS Network Protection plan and link to hub virtual networks."
  default     = true
}

variable "azure_ad_groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {}
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}
