# Get the current client configuration from the AzureRM provider.
# This is used to populate the root_parent_id variable with the
# current Tenant ID used as the ID for the "Tenant Root Group"
# Management Group.

data "azurerm_client_config" "core" {}

# Declare the Azure landing zones Terraform module
# and provide a base configuration.

module "enterprise_scale" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "6.3.1" # change this to your desired version, https://www.terraform.io/language/expressions/version-constraints

  default_location = "eastasia"

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }

  root_parent_id = data.azurerm_client_config.core.tenant_id
  root_id        = var.root_id
  root_name      = var.root_name
  # Disable custom library completely to avoid provider crashes
  library_path   = ""

  # Temporarily disable custom landing zones to avoid policy definition errors
  custom_landing_zones = {}
}

# Azure AD Groups Module
module "azure_ad_groups" {
  source = "./modules/azure_ad_groups"

  groups = var.azure_ad_groups

  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
  enable_directory_role_assignments = var.enable_directory_role_assignments

  tags = {
    Environment = "Production"
    ManagedBy   = "Terraform"
    Project     = "Landing Zone"
  }
}